/**
 * 地址工具类
 * 提供经纬度转地址、地址转经纬度等功能
 * 使用高德地图API
 */

// 引入高德地图SDK
import { AMapWX } from '../libs/amap-wx.130.js';
// 引入地图配置
import MapConfig from './MapConfig.js';

// 初始化高德地图实例
let amapInstance = null;

// 获取高德地图实例
function getAmapInstance() {
  if (!amapInstance) {
    amapInstance = new AMapWX({
      key: MapConfig.AMAP_KEY
    });
  }
  return amapInstance;
}

class AddressUtils {
  /**
   * 根据经纬度获取地址信息（逆地理编码）
   * @param {number} latitude 纬度
   * @param {number} longitude 经度
   * @param {object} options 选项
   * @param {boolean} options.showLoading 是否显示加载提示，默认false
   * @param {boolean} options.getPoi 是否获取POI信息，默认true
   * @returns {Promise<string>} 地址字符串
   */
  static getAddressFromLocation(latitude, longitude, options = {}) {
    const { showLoading = false } = options;

    return new Promise((resolve) => {
      if (!latitude || !longitude) {
        resolve('');
        return;
      }

      if (showLoading) {
        wx.showLoading({
          title: '获取地址中...'
        });
      }

      const amap = getAmapInstance();

      amap.getRegeo({
        location: `${longitude},${latitude}`, // 高德地图格式：经度,纬度
        success: (data) => {
          if (showLoading) {
            wx.hideLoading();
          }

          let address = '';
          if (data && data.length > 0) {
            const result = data[0];
            // 使用高德地图返回的格式化地址
            address = result.name || result.desc || '';
          }
          resolve(address);
        },
        fail: (error) => {
          if (showLoading) {
            wx.hideLoading();
          }
          console.error('AddressUtils: 高德地图地址解析失败', error);
          resolve('');
        }
      });
    });
  }

  /**
   * 根据经纬度获取详细地址信息（逆地理编码）
   * @param {number} latitude 纬度
   * @param {number} longitude 经度
   * @param {object} options 选项
   * @param {boolean} options.showLoading 是否显示加载提示，默认false
   * @returns {Promise<object>} 包含基础地址和详细地址的对象
   */
  static getDetailedAddressFromLocation(latitude, longitude, options = {}) {
    const { showLoading = false } = options;

    return new Promise((resolve) => {
      if (!latitude || !longitude) {
        resolve({ address: '', addressDetail: '' });
        return;
      }

      if (showLoading) {
        wx.showLoading({
          title: '获取地址中...'
        });
      }

      const amap = getAmapInstance();

      amap.getRegeo({
        location: `${longitude},${latitude}`, // 高德地图格式：经度,纬度
        success: (data) => {
          if (showLoading) {
            wx.hideLoading();
          }

          let address = '';
          let addressDetail = '';

          if (data && data.length > 0) {
            const result = data[0];

            // 从regeocodeData中获取详细信息
            if (result.regeocodeData) {
              const regeocode = result.regeocodeData;

              // 构建基础地址（省市区+街道）
              if (regeocode.addressComponent) {
                const component = regeocode.addressComponent;
                const addressParts = [];

                if (component.province) addressParts.push(component.province);
                if (component.city && component.city !== component.province) {
                  addressParts.push(component.city);
                }
                if (component.district) addressParts.push(component.district);
                if (component.streetNumber && component.streetNumber.street) {
                  addressParts.push(component.streetNumber.street);
                }

                address = addressParts.join('');
              }

              // 获取详细地址（POI信息或道路信息）
              if (regeocode.pois && regeocode.pois.length > 0) {
                // 优先使用最近的POI
                addressDetail = regeocode.pois[0].name;
              } else if (regeocode.roads && regeocode.roads.length > 0) {
                // 如果没有POI，使用道路信息
                addressDetail = regeocode.roads[0].name + '附近';
              }
            }

            // 如果上面的方法没有获取到地址，使用原来的方法
            if (!address) {
              address = result.name || result.desc || '';
            }
          }

          resolve({ address, addressDetail });
        },
        fail: (error) => {
          if (showLoading) {
            wx.hideLoading();
          }
          console.error('AddressUtils: 高德地图详细地址解析失败', error);
          resolve({ address: '', addressDetail: '' });
        }
      });
    });
  }

  /**
   * 根据地址获取经纬度信息（地理编码）
   * @param {string} address 地址
   * @param {object} options 选项
   * @param {boolean} options.showLoading 是否显示加载提示，默认false
   * @returns {Promise<object|null>} 包含lat和lng的对象，失败返回null
   */
  static getLocationFromAddress(address, options = {}) {
    const { showLoading = false } = options;

    return new Promise((resolve) => {
      if (!address) {
        resolve(null);
        return;
      }

      if (showLoading) {
        wx.showLoading({
          title: '搜索地址中...'
        });
      }

      const amap = getAmapInstance();

      amap.getGeo({
        options: {
          address: address
        },
        success: (data) => {
          if (showLoading) {
            wx.hideLoading();
          }

          if (data && data.geocodes && data.geocodes.length > 0) {
            const location = data.geocodes[0].location;
            if (location) {
              // 高德地图返回格式：经度,纬度
              const [lng, lat] = location.split(',').map(parseFloat);
              resolve({ lat, lng });
            } else {
              resolve(null);
            }
          } else {
            resolve(null);
          }
        },
        fail: (error) => {
          if (showLoading) {
            wx.hideLoading();
          }
          console.error('AddressUtils: 高德地图地址搜索失败', error);
          resolve(null);
        }
      });
    });
  }

  /**
   * 格式化地址显示
   * 优先显示地址，如果没有地址但有经纬度则显示经纬度坐标
   * @param {object} item 包含地址和经纬度信息的对象
   * @param {string} item.address 地址
   * @param {number} item.latitude 纬度
   * @param {number} item.longitude 经度
   * @returns {string} 格式化后的地址显示
   */
  static formatAddressDisplay(item) {
    if (!item) return '';

    // 如果有地址信息，直接使用（排除错误信息）
    if (item.address &&
        item.address !== '位置获取失败' &&
        item.address !== '位置解析失败' &&
        item.address !== '') {
      return item.address;
    }

    // 如果有经纬度但没有地址，显示经纬度坐标
    if (item.latitude && item.longitude) {
      return `位置: ${item.latitude}, ${item.longitude}`;
    }

    // 都没有则返回空
    return '';
  }

  /**
   * 批量解析地址信息
   * 对包含经纬度但缺少地址的记录进行批量地址解析
   * @param {Array} items 记录列表
   * @param {Function} updateCallback 更新回调函数，接收(index, address)参数
   * @returns {Promise<void>}
   */
  static async batchResolveAddresses(items, updateCallback) {
    if (!items || !Array.isArray(items) || typeof updateCallback !== 'function') {
      return;
    }

    // 找出需要地址解析的记录
    const needResolveItems = items
      .map((item, index) => ({ item, index }))
      .filter(({ item }) => {
        // 有经纬度但没有有效地址的记录
        return item.latitude &&
               item.longitude &&
               (!item.address ||
                item.address === '位置获取失败' ||
                item.address === '位置解析失败' ||
                item.address === '');
      });

    if (needResolveItems.length === 0) {
      return;
    }

    // 逐个解析地址（避免并发过多请求）
    for (const { item, index } of needResolveItems) {
      try {
        const address = await this.getAddressFromLocation(
          item.latitude,
          item.longitude,
          { showLoading: false } // 静默解析，不显示加载提示
        );

        if (address) {
          // 调用更新回调
          updateCallback(index, address);
        }
      } catch (error) {
        console.error(`AddressUtils: 地址解析失败 (${item.latitude}, ${item.longitude})`, error);
        // 解析失败时不做处理，保持原有显示
      }

      // 添加小延迟避免请求过于频繁
      await new Promise(resolve => setTimeout(resolve, 200));
    }
  }

  /**
   * 打开导航
   * 使用微信内置地图打开导航
   * @param {object} location 位置信息
   * @param {string} location.address 地址
   * @param {string} location.remark 备注
   * @param {number} location.latitude 纬度
   * @param {number} location.longitude 经度
   */
  static async openNavigation(location) {
    const { address, remark, latitude, longitude } = location;

    if (!address) {
      wx.showToast({
        title: '地址信息不完整',
        icon: 'none'
      });
      return;
    }

    // 构建完整地址
    const fullAddress = remark ? `${address}(${remark})` : address;

    // 优先使用订单中的经纬度数据
    if (latitude && longitude) {
      const lat = parseFloat(latitude);
      const lng = parseFloat(longitude);

      // 验证经纬度数据的有效性
      if (!isNaN(lat) && !isNaN(lng) && lat >= -90 && lat <= 90 && lng >= -180 && lng <= 180) {
        wx.openLocation({
          latitude: lat,
          longitude: lng,
          name: fullAddress,
          address: fullAddress,
          scale: 18
        });
        return;
      }
    }

    // 如果没有经纬度数据或数据无效，则使用地址搜索
    try {
      const locationResult = await this.getLocationFromAddress(fullAddress, { showLoading: true });
      
      if (locationResult) {
        wx.openLocation({
          latitude: locationResult.lat,
          longitude: locationResult.lng,
          name: fullAddress,
          address: fullAddress,
          scale: 18
        });
      } else {
        wx.showToast({
          title: '无法找到该地址',
          icon: 'none'
        });
      }
    } catch (error) {
      console.error('AddressUtils: 导航失败', error);
      wx.showToast({
        title: '导航功能暂时不可用',
        icon: 'none'
      });
    }
  }
}

export default AddressUtils;
