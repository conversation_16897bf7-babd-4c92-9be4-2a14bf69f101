<!--pages/guide/index.wxml-->
<view class="demo-page">
  <!-- 头部区域 -->
  <view class="header-section">
    <view class="logo-wrapper">
      <image src="//xian7.zos.ctyun.cn/pet/static/logo.png" mode="widthFix" class="logo-img"></image>
      <text class="app-title">贝宠接单大厅</text>
    </view>
    <view class="demo-tip">
      <text class="tip-icon">👀</text>
      <text class="tip-text">体验模式</text>
    </view>
  </view>

  <!-- 订单类型切换 -->
  <view class="tab-container">
    <view class="tab-list">
      <view
        wx:for="{{orderTabs}}"
        wx:key="status"
        class="tab-item {{currentTab === item.status ? 'active' : ''}}"
        data-status="{{item.status}}"
        bindtap="switchTab"
      >
        {{item.name}}
      </view>
    </view>
  </view>

  <!-- 订单列表 -->
  <view class="order-container">
    <view wx:if="{{orderList.length === 0}}" class="empty-state">
      <text class="empty-text">暂无{{currentTab === 'wash' ? '洗护' : '美容'}}订单</text>
    </view>

    <view wx:for="{{orderList}}" wx:key="id" class="order-card" data-item="{{item}}" bindtap="viewOrderDetail">
      <!-- 订单头部 -->
      <view class="order-header">
        <view class="order-info">
          <text class="order-id">{{item.orderId}}</text>
          <text class="order-time">{{item.orderTime}}</text>
        </view>
        <view class="order-status">
          <text class="status-text">待接单</text>
        </view>
      </view>

      <!-- 服务信息 -->
      <view class="service-section">
        <view class="service-icon">
          <image src="{{item.serviceLogo}}" mode="aspectFit"></image>
        </view>
        <view class="service-details">
          <text class="service-name">{{item.serviceName[0]}}</text>
          <text class="service-extra" wx:if="{{item.extraServive.length > 0}}">
            增项：{{item.extraServive.join('、')}}
          </text>
          <text class="expect-time">期望服务时间：{{item.expectTime}}</text>
        </view>
      </view>

      <!-- 客户信息 -->
      <view class="customer-section">
        <view class="customer-info">
          <text class="customer-name">客户：{{item.customerName}}</text>
          <text class="customer-address">地址：{{item.address}}</text>
        </view>
      </view>

      <!-- 价格信息 -->
      <view class="price-section">
        <view class="price-info">
          <text class="original-price" wx:if="{{item.originalPrice !== item.totalFee}}">原价：¥{{item.originalPrice}}</text>
          <text class="total-price">实付：¥{{item.totalFee}}</text>
        </view>
        <view class="action-btn" catchtap="showLoginTip">
          <text>接单</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 底部登录按钮 -->
  <view class="bottom-actions">
    <button class="full-feature-btn" bindtap="goToLogin">
      <text class="btn-icon">🔑</text>
      <text class="btn-text">使用完整功能</text>
    </button>
    <view class="login-tip">
      <text>登录后可接单、查看详细信息等</text>
    </view>
  </view>
</view>
