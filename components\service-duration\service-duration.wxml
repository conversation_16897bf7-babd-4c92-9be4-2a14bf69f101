<!-- 服务时长统计 -->
<view class="service-duration-section" wx:if="{{orderDetail.status === '服务中' || serviceDurationRecords.length > 0}}">
  <view wx:if="{{showServiceDuration}}">
    <!-- 主服务时长统计 -->
    <view class="main-service-duration" wx:if="{{orderDetail.orderDetails && orderDetail.orderDetails.length > 0}}">
      <view class="service-section-title">主服务</view>
      <view class="service-duration-item main-service" wx:for="{{orderDetail.orderDetails}}" wx:key="id">
        <view class="duration-info">
          <view class="service-name-row">
            <text class="service-name">{{item.service.serviceName}}</text>
            <text class="service-status {{item.serviceStatus}}">{{item.serviceStatusText}}</text>
          </view>
          <view class="time-info" wx:if="{{item.startTime}}">
            <text class="start-time" wx:if="{{item.startTime}}">开始时间：{{item.startTime}}</text>
            <text class="current-duration" wx:if="{{item.currentDuration}}">已用时：{{item.currentDuration}}</text>
            <text class="duration" wx:if="{{item.duration}}">用时：{{item.duration}}</text>
          </view>
        </view>
        <view class="duration-actions" wx:if="{{orderDetail.status === '服务中'}}">
          <view class="action-btn start-btn"
                wx:if="{{item.showStartBtn}}"
                bindtap="onStartMainService"
                data-order-detail-id="{{item.id}}"
                data-service-id="{{item.service.id}}"
                data-service-name="{{item.service.serviceName}}">
            开始
          </view>
          <view class="action-btn end-btn"
                wx:if="{{item.showEndBtn}}"
                bindtap="onEndServiceDuration"
                data-record-id="{{item.recordId}}"
                data-service-name="{{item.service.serviceName}}">
            完成
          </view>
        </view>
      </view>
    </view>

    <!-- 增项服务时长统计 -->
    <view class="additional-service-duration" wx:if="{{allAdditionalServices.length > 0}}">
      <view class="service-section-title">增项服务</view>
      <view class="service-duration-item additional-service" wx:for="{{allAdditionalServices}}" wx:key="uniqueKey" wx:if="{{item.needDurationTracking}}">
        <view class="duration-info">
          <view class="service-name-row">
            <text class="service-name">{{item.displayName || item.serviceName}}</text>
            <text class="service-status {{item.serviceStatus}}">{{item.serviceStatusText}}</text>
          </view>
          <view class="time-info" wx:if="{{item.startTime}}">
            <text class="start-time" wx:if="{{item.startTime}}">开始时间：{{item.startTime}}</text>
            <text class="current-duration" wx:if="{{item.currentDuration}}">已用时：{{item.currentDuration}}</text>
            <text class="duration" wx:if="{{item.duration}}">用时：{{item.duration}}</text>
          </view>
          <!-- 未付款提示 -->
          <view class="unpaid-notice" wx:if="{{item.type === 'additional' && (item.status === 'pending_payment' || item.status === 'confirmed')}}">
            <text class="notice-text">⚠️ 此服务尚未付款，无法开始计时</text>
          </view>
        </view>
        <view class="duration-actions" wx:if="{{item.showDurationActions}}">
          <view class="action-btn start-btn"
                wx:if="{{item.showStartBtn}}"
                bindtap="onStartAdditionalService"
                data-service-type="{{item.type}}"
                data-service-id="{{item.originalOrderId || item.id}}"
                data-additional-service-id="{{item.additionalServiceId}}"
                data-order-detail-id="{{item.orderDetailId}}"
                data-service-name="{{item.displayName || item.serviceName}}"
                data-sequence-number="{{item.sequenceNumber}}">
            开始
          </view>
          <view class="action-btn end-btn"
                wx:if="{{item.showEndBtn}}"
                bindtap="onEndServiceDuration"
                data-record-id="{{item.recordId}}"
                data-service-name="{{item.displayName || item.serviceName}}">
            完成
          </view>
        </view>
      </view>
    </view>

    <!-- 已完成的服务时长记录汇总 -->
    <view class="completed-services-summary" wx:if="{{serviceDurationRecords.length > 0}}">
      <view class="service-section-title">服务时长汇总</view>
      <view class="summary-info">
        <view class="summary-item">
          <text class="summary-label">总服务项目：</text>
          <text class="summary-value">{{serviceDurationStatistics.totalRecords || serviceDurationRecords.length}}项</text>
        </view>
        <view class="summary-item">
          <text class="summary-label">已完成：</text>
          <text class="summary-value">{{serviceDurationStatistics.completedRecords}}项</text>
        </view>
        <view class="summary-item">
          <text class="summary-label">进行中：</text>
          <text class="summary-value">{{serviceDurationStatistics.inProgressRecords || 0}}项</text>
        </view>
        <view class="summary-item">
          <text class="summary-label">总用时：</text>
          <text class="summary-value">{{serviceDurationStatistics.totalDurationText}}</text>
        </view>
        <view class="summary-item" wx:if="{{serviceDurationStatistics.mainServiceDuration}}">
          <text class="summary-label">主服务用时：</text>
          <text class="summary-value">{{serviceDurationStatistics.mainServiceDurationText}}</text>
        </view>
        <view class="summary-item" wx:if="{{serviceDurationStatistics.additionalServiceDuration}}">
          <text class="summary-label">增项用时：</text>
          <text class="summary-value">{{serviceDurationStatistics.additionalServiceDurationText}}</text>
        </view>
      </view>
    </view>
  </view>
</view>
