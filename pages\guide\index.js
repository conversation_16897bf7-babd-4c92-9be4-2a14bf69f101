import Session from '../../common/Session';
import { formatDate, formatNormalDate } from '../utils/util';

Page({
  data: {
    // 订单类型标签
    orderTabs: [
      { name: '洗护单', status: 'wash' },
      { name: '美容单', status: 'beauty' }
    ],
    currentTab: 'wash', // 当前选中的标签
    orderList: [], // 订单列表
    isDemo: true, // 标识这是演示模式
  },

  onLoad() {
    // 检查用户是否已登录
    const userInfo = Session.getUser();
    if (userInfo && userInfo.id) {
      // 已登录，直接跳转到首页
      wx.switchTab({
        url: '/pages/index/index',
      });
      return;
    }

    // 未登录，加载模拟数据
    this.loadDemoOrders();
  },

  // 加载模拟订单数据
  loadDemoOrders() {
    const demoOrders = [
      {
        id: 'demo_001',
        orderId: 'PET202412***001',
        serviceName: ['基础洗护'],
        serviceLogo: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjQwIiBoZWlnaHQ9IjQwIiByeD0iOCIgZmlsbD0iIzJGODNGRiIvPgo8cGF0aCBkPSJNMTIgMTZIMjhWMjRIMTJWMTZaIiBmaWxsPSJ3aGl0ZSIvPgo8L3N2Zz4K',
        orderTime: formatDate(new Date(Date.now() - 2 * 60 * 60 * 1000)), // 2小时前
        expectTime: formatNormalDate(new Date(Date.now() + 4 * 60 * 60 * 1000)), // 4小时后
        originalPrice: 88.00,
        totalFee: 78.00,
        extraServive: ['除臭护理'],
        customerName: '张**',
        address: '朝阳区***小区',
        remark: '小型犬，比较温顺',
        phone: '138****8888',
        petInfo: '泰迪，2岁，雄性',
        status: 'paid',
        type: 'wash'
      },
      {
        id: 'demo_002',
        orderId: 'PET202412***002',
        serviceName: ['精致美容'],
        serviceLogo: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjQwIiBoZWlnaHQ9IjQwIiByeD0iOCIgZmlsbD0iI0ZGNDc1NyIvPgo8cGF0aCBkPSJNMTIgMTZIMjhWMjRIMTJWMTZaIiBmaWxsPSJ3aGl0ZSIvPgo8L3N2Zz4K',
        orderTime: formatDate(new Date(Date.now() - 1 * 60 * 60 * 1000)), // 1小时前
        expectTime: formatNormalDate(new Date(Date.now() + 6 * 60 * 60 * 1000)), // 6小时后
        originalPrice: 168.00,
        totalFee: 158.00,
        extraServive: ['造型设计', '指甲修剪'],
        customerName: '李**',
        address: '海淀区***大厦',
        remark: '需要造型，主人要求可爱风格',
        phone: '139****9999',
        petInfo: '比熊，3岁，雌性',
        status: 'paid',
        type: 'beauty'
      },
      {
        id: 'demo_003',
        orderId: 'PET202412***003',
        serviceName: ['深度清洁'],
        serviceLogo: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjQwIiBoZWlnaHQ9IjQwIiByeD0iOCIgZmlsbD0iIzJGODNGRiIvPgo8cGF0aCBkPSJNMTIgMTZIMjhWMjRIMTJWMTZaIiBmaWxsPSJ3aGl0ZSIvPgo8L3N2Zz4K',
        orderTime: formatDate(new Date(Date.now() - 30 * 60 * 1000)), // 30分钟前
        expectTime: formatNormalDate(new Date(Date.now() + 3 * 60 * 60 * 1000)), // 3小时后
        originalPrice: 128.00,
        totalFee: 118.00,
        extraServive: ['药浴护理'],
        customerName: '王**',
        address: '西城区***路',
        remark: '皮肤敏感，需要温和处理',
        phone: '136****6666',
        petInfo: '金毛，5岁，雄性',
        status: 'paid',
        type: 'wash'
      }
    ];

    // 根据当前选中的标签过滤订单
    const filteredOrders = demoOrders.filter(order => order.type === this.data.currentTab);

    this.setData({
      orderList: filteredOrders
    });
  },

  // 切换订单状态标签
  switchTab(e) {
    const status = e.currentTarget.dataset.status;
    this.setData({
      currentTab: status
    });
    this.loadDemoOrders();
  },

  // 查看订单详情（演示模式）
  viewOrderDetail(e) {
    const orderInfo = e.currentTarget.dataset.item;
    wx.setStorageSync('demoOrderInfo', orderInfo);
    wx.navigateTo({
      url: `/pages/guide/orderDetail/index`,
    });
  },

  // 显示登录提示
  showLoginTip() {
    wx.showModal({
      title: '提示',
      content: '此功能需要登录后使用，是否前往登录？',
      confirmText: '去登录',
      cancelText: '继续浏览',
      success: (res) => {
        if (res.confirm) {
          this.goToLogin();
        }
      }
    });
  },

  // 跳转到登录页面
  goToLogin() {
    wx.navigateTo({
      url: '/pages/login/index',
    });
  },

  onShow() {
    // 每次显示页面时检查登录状态
    const userInfo = Session.getUser();
    if (userInfo && userInfo.id) {
      // 已登录，直接跳转到首页
      wx.switchTab({
        url: '/pages/index/index',
      });
    }
  }
});
