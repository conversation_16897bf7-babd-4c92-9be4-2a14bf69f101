import { formatNormalDate } from '../../pages/utils/util';

Component({
  properties: {
    orderDetail: {
      type: Object,
      value: {}
    },
    specialNoteData: {
      type: Object,
      value: null,
      observer: function(newVal) {
        if (newVal && newVal.createdAt) {
          // 格式化时间显示
          this.setData({
            formattedCreatedAt: formatNormalDate(newVal.createdAt)
          });
        }
      }
    }
  },

  data: {
    formattedCreatedAt: ''
  },

  methods: {
    // 显示特殊情况说明弹窗
    onShowSpecialNote() {
      this.triggerEvent('showSpecialNote');
    },

    // 预览特殊情况说明图片
    onPreviewPhoto(e) {
      const url = e.currentTarget.dataset.url;
      const urls = e.currentTarget.dataset.urls;

      wx.previewImage({
        current: url,
        urls: urls
      });
    }
  }
});
