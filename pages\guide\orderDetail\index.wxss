/* pages/guide/orderDetail/index.wxss */
.demo-detail-page {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 200rpx;
}

/* 演示横幅 */
.demo-banner {
  background: linear-gradient(135deg, #ff9800 0%, #ffb74d 100%);
  color: white;
  padding: 20rpx 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.demo-icon {
  margin-right: 10rpx;
  font-size: 28rpx;
}

.demo-text {
  font-size: 26rpx;
  font-weight: bold;
}

/* 订单基本信息 */
.order-basic-info {
  background: white;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15rpx;
}

.order-id {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.order-status {
  background: #fff3e0;
  color: #ff9800;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
}

.status-text {
  font-size: 24rpx;
}

.order-time {
  font-size: 26rpx;
  color: #666;
}

/* 信息卡片通用样式 */
.service-info-card,
.customer-info-card,
.price-info-card {
  background: white;
  margin: 0 0 20rpx 0;
  border-radius: 20rpx;
  overflow: hidden;
}

.card-title {
  background: #f8f9fa;
  padding: 25rpx 30rpx;
  display: flex;
  align-items: center;
  border-bottom: 1rpx solid #eee;
}

.title-icon {
  margin-right: 15rpx;
  font-size: 32rpx;
}

.title-text {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
}

/* 服务信息 */
.service-content {
  padding: 30rpx;
}

.service-item {
  display: flex;
  margin-bottom: 20rpx;
  align-items: flex-start;
}

.service-item:last-child {
  margin-bottom: 0;
}

.service-label {
  font-size: 28rpx;
  color: #666;
  width: 160rpx;
  flex-shrink: 0;
}

.service-value {
  font-size: 28rpx;
  color: #333;
  flex: 1;
  line-height: 1.5;
}

/* 客户信息 */
.customer-content {
  padding: 30rpx;
}

.customer-item {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.customer-item:last-child {
  margin-bottom: 0;
}

.customer-label {
  font-size: 28rpx;
  color: #666;
  width: 160rpx;
  flex-shrink: 0;
}

.customer-value {
  font-size: 28rpx;
  color: #333;
  flex: 1;
}

.phone-btn,
.nav-btn {
  width: 60rpx;
  height: 60rpx;
  background: #2f83ff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 20rpx;
}

.phone-icon,
.nav-icon {
  color: white;
  font-size: 24rpx;
}

/* 价格信息 */
.price-content {
  padding: 30rpx;
}

.price-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15rpx;
}

.price-item:last-child {
  margin-bottom: 0;
}

.price-label {
  font-size: 28rpx;
  color: #666;
}

.original-price {
  font-size: 26rpx;
  color: #999;
  text-decoration: line-through;
}

.total-price {
  font-size: 32rpx;
  font-weight: bold;
  color: #ff4757;
}

/* 底部操作 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  padding: 30rpx;
  border-top: 1rpx solid #eee;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.accept-btn {
  background: linear-gradient(135deg, #2f83ff 0%, #4a9eff 100%);
  color: white;
  border-radius: 50rpx;
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 15rpx;
}

.btn-icon {
  margin-right: 10rpx;
  font-size: 36rpx;
}

.btn-text {
  font-size: 32rpx;
}

.demo-tip {
  text-align: center;
}

.demo-tip text {
  font-size: 24rpx;
  color: #999;
}

/* 加载状态 */
.loading-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  font-size: 28rpx;
  color: #666;
}
