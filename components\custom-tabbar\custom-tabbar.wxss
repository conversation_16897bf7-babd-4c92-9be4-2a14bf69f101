@import "/common/diygw-ui/index.wxss"; /* 确保路径正确 */
@import "/common/tabbar-layout.wxss"; /* 引入底部导航布局通用样式 */

/* 固定底部导航高度，避免页面加载时高度不确定导致的布局问题 */
.custom-bottom-bar {
  height: 100rpx;
  min-height: 100rpx;
  padding-bottom: env(safe-area-inset-bottom);
  box-sizing: border-box;
  /* 确保底部导航在页面加载时就有正确的高度 */
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 1000;
}

.custom-bottom-bar .badge{
  top: 0rpx;
  right: calc(50% - 50rpx);
}

/* 底部导航图片样式 - 防止拉伸 */
.image-sm{
  width: 56rpx !important;
  height: 56rpx !important;
  margin: 10rpx;
  flex-shrink: 0;
  object-fit: contain; /* 确保图片不被拉伸 */
}