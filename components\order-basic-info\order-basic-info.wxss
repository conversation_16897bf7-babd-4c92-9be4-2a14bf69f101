.order-info {
  background-color: #fff;
  padding: 20rpx;
  border-radius: 10rpx;
  margin-bottom: 20rpx;
}

.order-content {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.product-image {
  width: 150rpx;
  height: 150rpx;
  margin-right: 20rpx;
  border-radius: 10rpx;
}

.product-info {
  display: flex;
  flex-direction: column;
  flex: 1;
}

.product-service {
  background: rgba(47, 131, 255, 0.1);
  border-radius: 8px;
  padding: 8rpx 12rpx;
  width: 100%;
  line-height: 50rpx;
  margin-top: 16rpx;
}

.product-name {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 10rpx;
}

.paid-money {
  text-align: right;
  font-size: 32rpx;
  color: #333;
  font-weight: bold;
}

.order-details {
  border-top: 1rpx solid #eee;
  padding-top: 20rpx;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: baseline;
  min-height: 60rpx;
  padding: 8rpx 0;
  gap: 20rpx;
  width: 100%;
}

.label-row {
  display: flex;
  align-items: center;
  flex: 1;
}

.label {
  font-size: 28rpx;
  color: #666;
  flex-shrink: 0;
  line-height: 1.4;
}

.edit-btn {
  margin-left: 10rpx;
  padding: 4rpx 8rpx;
  background-color: #2f83ff;
  border-radius: 4rpx;
}

.edit-text {
  font-size: 24rpx;
  color: #fff;
}

.content {
  flex: 1.5;
  font-size: 28rpx;
  color: #333;
  text-align: right;
  word-wrap: break-word;
  word-break: break-all;
  line-height: 1.4;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 8rpx;
}

.user-remark {
  color: #666;
  font-style: italic;
}

.original-price {
  text-decoration: line-through;
  color: #999;
}

.flex {
  display: flex;
}

.align-center {
  align-items: center;
}

.justify-between {
  justify-content: space-between;
}

/* 备注照片 */
.remark-photos {
  display: flex;
  flex-wrap: wrap;
  gap: 8rpx;
}

.remark-photo {
  width: 100rpx;
  height: 100rpx;
  border-radius: 6rpx;
}
