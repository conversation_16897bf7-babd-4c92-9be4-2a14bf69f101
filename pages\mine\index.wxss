/* pages/mine/index.wxss */
.containermine {
  background-color: #f5f5f5;
  padding: 24rpx;
  /* 为底部导航预留固定空间，避免页面加载时高度计算不准确 */
  padding-bottom: calc(100rpx + env(safe-area-inset-bottom));
  background: url('//xian7.zos.ctyun.cn/pet/static/minebj.png') no-repeat;
  background-size: contain;
  box-sizing: border-box;
  min-height: 100vh;
}
.title-left{
  padding-left: 40rpx;
  font-size: 36rpx;
  font-weight: bold;
}
.flex12-clz {
  width: calc(100% - 40rpx) !important;
  margin: 20rpx 20rpx 40rpx;
}

.flex10-clz {
  color: #333;
}

.image-round {
  border-radius: 120rpx;
  overflow: hidden;
}

.image7-size {
  height: 120rpx !important;
  width: 120rpx !important;
}

.flex20-clz {
  padding: 0rpx 24rpx;
  flex: 1;
}

.flex13-clz {
  flex: 1;
}

.text12-clz {
  font-weight: bold;
  font-size: 32rpx !important;
}

.flex14-clz {
  flex: 1;
  color: rgba(153, 153, 153, 1);
}

/* 职位信息样式 */
.position-text {
  font-size: 28rpx;
  color: rgba(47, 131, 255, 1);
  font-weight: 500;
}

.icon5 {
  font-size: 40rpx;
  color: #4A90E2;
  padding: 10rpx;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.icon5:active {
  background-color: rgba(74, 144, 226, 0.1);
  transform: scale(0.95);
}

.vipbg {
  clear: both;
  position: relative;
  border-radius: 24rpx;
  overflow: hidden;
}

.vip-contain {
  clear: both;
  position: absolute;
  top: 30rpx;
  left: 30rpx;
  right: 30rpx;
  bottom: 30rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.viptext {
  color: rgba(47, 131, 255, 1);
  font-weight: bold;
  font-size: 38rpx;
  font-style: italic;
}

.flex16-clz {
  clear: both;
  margin: -10rpx 0 30rpx;
  background-color: white;
  border-radius: 24rpx;
  overflow: hidden;
  box-shadow: 0px 0px 8px 0px rgba(0, 0, 0, 0.1);
  padding: 30rpx;
}

.flex17-clz {
  padding: 0;
}

.text13-clz {
  color: #333;
  font-weight: bold;
  font-size: 34rpx !important;
}

.flex30-clz {
  border-radius: 32rpx;
}

.text32-clz {
  text-align: right;
}

.flex6-clz {
  margin: 10rpx 0rpx 10rpx -30rpx;
  width: calc(100% + 30rpx - 0rpx) !important;
}

.flex46-clz {
  border-radius: 24rpx;
  margin: 15rpx 0rpx 0 30rpx;
  overflow: hidden;
}
.account-info{
  text-align: center;
}
.action-part {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 5.2rem;  
}
.action-part .title{
  font-size: 32rpx;
  color:rgba(153, 153, 153, 1);
}
.action-part .bold{
  font-size: 40rpx;
  font-weight: bold;
}
.action-part text{
  color:rgba(47, 131, 255, 1);
  padding: 0 20rpx;
}
.action-part .diy-icon-titles{
  color:rgba(0, 0, 0, 0.1);
}

/* 评价详情样式 */
.rating-stars {
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 30rpx;
}

.rating-stars .star {
  margin: 0 2rpx;
  padding: 0 5rpx;
}

.star.filled {
  color: #FFD700; /* 金色 */
}

.star.half {
  background: linear-gradient(90deg, #FFD700 50%, #E0E0E0 50%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.star.empty {
  color: #E0E0E0; /* 灰色 */
}

.review-count {
  font-size: 24rpx;
  color: rgba(153, 153, 153, 1);
  margin-left: 8rpx;
}
.image7-clz {
  border-radius: 24rpx;
  overflow: hidden;
}

.image7-size {
  height: 160rpx !important;
  width: 160rpx !important;
}

.flex38-clz {
  margin: 30rpx 0;
  background-color: white;
  border-radius: 24rpx;
  overflow: hidden;
  box-shadow: 0px 0px 8px 0px rgba(0, 0, 0, 0.1);
  padding: 30rpx;
}

.icon6 {
  font-size: 32rpx;
}

.flex18-clz {
  padding: 10rpx;
}

.image-size {
  height: 80rpx !important;
  width: 80rpx !important;
}

.image-sm {
  height: 60rpx !important;
  width: 60rpx !important;
}

.text11-clz {
  padding: 10rpx;
}
.btn-login{
  background: rgba(255, 67, 145, 1);
  box-shadow: 0px 3px 0px 0px rgba(255, 67, 145, 0.3);
  color: #fff;
  border-radius: 40rpx;
  line-height: 60rpx;
  font-size: 24rpx;
  padding: 0 40rpx;
}

/* 重置button样式为card */
.card {
  background-color: white;
  line-height: normal;
}
.card::after {
  border: none;
}

/* 位置服务状态样式 */
.location-status {
  transition: color 0.3s ease;
}

.location-status.active {
  color: #4CAF50; /* 绿色表示运行中 */
}

.location-status.inactive {
  color: #FF5722; /* 红色表示已停止 */
}

/* 引入底部导航布局通用样式 */
@import "/common/tabbar-layout.wxss";