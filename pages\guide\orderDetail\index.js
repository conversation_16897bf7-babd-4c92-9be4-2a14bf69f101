import Session from '../../../common/Session';

Page({
  data: {
    orderInfo: null,
    isDemo: true,
  },

  onLoad() {
    // 检查用户是否已登录
    const userInfo = Session.getUser();
    if (userInfo && userInfo.id) {
      // 已登录，跳转到真实的订单详情页
      wx.redirectTo({
        url: '/pages/orders/orderDetail/index',
      });
      return;
    }

    // 获取演示订单信息
    const demoOrderInfo = wx.getStorageSync('demoOrderInfo');
    if (demoOrderInfo) {
      this.setData({
        orderInfo: demoOrderInfo
      });
    } else {
      // 如果没有订单信息，返回上一页
      wx.navigateBack();
    }
  },

  // 显示登录提示
  showLoginTip() {
    wx.showModal({
      title: '提示',
      content: '此功能需要登录后使用，是否前往登录？',
      confirmText: '去登录',
      cancelText: '继续浏览',
      success: (res) => {
        if (res.confirm) {
          wx.navigateTo({
            url: '/pages/login/index',
          });
        }
      }
    });
  },

  // 拨打电话（演示）
  makePhoneCall() {
    wx.showModal({
      title: '演示模式',
      content: '这是演示功能，实际使用需要登录',
      showCancel: false,
      confirmText: '知道了'
    });
  },

  // 打开导航（演示）
  openNavigation() {
    wx.showModal({
      title: '演示模式',
      content: '这是演示功能，实际使用需要登录',
      showCancel: false,
      confirmText: '知道了'
    });
  },

  // 接单操作（演示）
  acceptOrder() {
    this.showLoginTip();
  }
});
