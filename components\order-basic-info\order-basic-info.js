Component({
  properties: {
    orderDetail: {
      type: Object,
      value: {}
    }
  },

  data: {},

  methods: {
    // 修改上门时间
    onReschedule() {
      this.triggerEvent('reschedule');
    },

    // 修改服务地址
    onEditAddress() {
      this.triggerEvent('editAddress');
    },

    // 打开导航
    onOpenNavigation(e) {
      const { address, remark, latitude, longitude } = e.currentTarget.dataset;
      this.triggerEvent('openNavigation', {
        address,
        remark,
        latitude,
        longitude
      });
    },

    // 预览照片
    previewPhoto(e) {
      const { url, urls } = e.currentTarget.dataset;
      wx.previewImage({
        current: url,
        urls: urls
      });
    }
  }
});
