/* pages/guide/index.wxss */
.demo-page {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 200rpx;
}

/* 头部区域 */
.header-section {
  background: linear-gradient(135deg, #2f83ff 0%, #4a9eff 100%);
  padding: 40rpx 30rpx 30rpx;
  color: white;
}

.logo-wrapper {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.logo-img {
  width: 80rpx;
  height: 80rpx;
  margin-right: 20rpx;
}

.app-title {
  font-size: 36rpx;
  font-weight: bold;
}

.demo-tip {
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.2);
  padding: 15rpx 20rpx;
  border-radius: 30rpx;
  font-size: 24rpx;
}

.tip-icon {
  margin-right: 10rpx;
  font-size: 28rpx;
}

.tip-text {
  font-size: 24rpx;
}

/* 标签切换 */
.tab-container {
  background: white;
  padding: 0 30rpx;
  border-bottom: 1rpx solid #eee;
}

.tab-list {
  display: flex;
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 30rpx 0;
  font-size: 28rpx;
  color: #666;
  position: relative;
}

.tab-item.active {
  color: #2f83ff;
  font-weight: bold;
}

.tab-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 60rpx;
  height: 4rpx;
  background: #2f83ff;
  border-radius: 2rpx;
}

/* 订单容器 */
.order-container {
  padding: 20rpx 30rpx;
}

.empty-state {
  text-align: center;
  padding: 100rpx 0;
  color: #999;
}

.empty-text {
  font-size: 28rpx;
}

/* 订单卡片 */
.order-card {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.order-info {
  display: flex;
  flex-direction: column;
}

.order-id {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 5rpx;
}

.order-time {
  font-size: 24rpx;
  color: #999;
}

.order-status {
  background: #fff3e0;
  color: #ff9800;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
}

.status-text {
  font-size: 24rpx;
}

/* 服务信息 */
.service-section {
  display: flex;
  align-items: flex-start;
  margin-bottom: 20rpx;
}

.service-icon {
  width: 80rpx;
  height: 80rpx;
  margin-right: 20rpx;
  flex-shrink: 0;
}

.service-icon image {
  width: 100%;
  height: 100%;
  border-radius: 10rpx;
}

.service-details {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.service-name {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.service-extra {
  font-size: 24rpx;
  color: #2f83ff;
  margin-bottom: 8rpx;
}

.expect-time {
  font-size: 24rpx;
  color: #666;
}

/* 客户信息 */
.customer-section {
  margin-bottom: 20rpx;
}

.customer-info {
  display: flex;
  flex-direction: column;
}

.customer-name {
  font-size: 26rpx;
  color: #333;
  margin-bottom: 5rpx;
}

.customer-address {
  font-size: 24rpx;
  color: #666;
}

/* 价格信息 */
.price-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-top: 1rpx solid #eee;
  padding-top: 20rpx;
}

.price-info {
  display: flex;
  flex-direction: column;
}

.original-price {
  font-size: 24rpx;
  color: #999;
  text-decoration: line-through;
  margin-bottom: 5rpx;
}

.total-price {
  font-size: 32rpx;
  font-weight: bold;
  color: #ff4757;
}

.action-btn {
  background: #2f83ff;
  color: white;
  padding: 15rpx 30rpx;
  border-radius: 30rpx;
  font-size: 26rpx;
}

/* 底部操作 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  padding: 30rpx;
  border-top: 1rpx solid #eee;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.full-feature-btn {
  background: linear-gradient(135deg, #2f83ff 0%, #4a9eff 100%);
  color: white;
  border-radius: 50rpx;
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 15rpx;
}

.btn-icon {
  margin-right: 10rpx;
  font-size: 36rpx;
}

.btn-text {
  font-size: 32rpx;
}

.login-tip {
  text-align: center;
}

.login-tip text {
  font-size: 24rpx;
  color: #999;
}
