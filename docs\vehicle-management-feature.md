# 车辆管理功能使用说明

## 功能概述

车辆管理功能允许员工查看和更新与自己绑定的车辆信息，包括：
- 里程数（公里）
- 外观描述
- 保险到期时间
- 行驶证到期时间
- 物资清单

## 功能入口

在"我的"页面中，点击"车辆管理"图标即可进入车辆管理页面。

## 页面功能

### 1. 车辆基本信息查看
- 车牌号
- 车辆类型
- 车辆状态

### 2. 车辆状态修改
- 点击车辆状态区域（📊 状态文字）弹出状态修改弹窗
- 可以选择常见状态：空闲、忙碌、保养中
- 可以自定义输入状态，最大长度6个字符
- 点击"确认"提交更新，点击"取消"放弃修改

### 3. 车辆详细信息编辑
- 点击"编辑"按钮进入编辑模式
- 可以修改里程数、外观描述、保险到期时间、行驶证到期时间、物资清单
- 点击"保存"提交更新，点击"取消"放弃修改

### 4. 数据验证
- 车辆状态：最大长度6个字符，不能为空
- 里程数：必须为数字，范围0-999999
- 外观描述：最大长度1000字符
- 物资清单：最大长度2000字符
- 日期：通过日期选择器选择，范围为今天到10年后

### 4. 更新记录
显示最后一次更新的时间和更新人信息。

## API接口

### 获取员工车辆信息
```
GET /openapi/vehicles/employee/{employeeId}
```

### 更新车辆信息
```
PUT /openapi/vehicles/{vehicleId}/info?employeeId={employeeId}
```

### 更新车辆状态
```
PUT /openapi/vehicles/{vehicleId}/status?employeeId={employeeId}
```

### 获取车辆详细信息
```
GET /openapi/vehicles/{vehicleId}
```

## 权限控制

- 员工只能查看和更新与自己绑定的车辆信息
- 未分配车辆的员工只会看到简单的提示界面，不显示任何车辆管理功能

## 错误处理

- 网络错误：显示"加载失败"或"保存失败"提示
- 权限错误：显示相应的错误信息
- 数据验证错误：显示具体的验证错误信息
- 未分配车辆：显示简洁的提示界面，引导用户联系管理员

## 技术实现

### 文件结构
```
api/
  config.js                 # API配置
  modules/vehicle.js         # 车辆管理API模块

pages/mine/vehicle/
  index.js                  # 页面逻辑
  index.wxml                # 页面结构
  index.wxss                # 页面样式
  index.json                # 页面配置
```

### 主要功能模块
1. **数据加载**：从API获取车辆信息
2. **表单编辑**：支持文本输入和日期选择
3. **数据验证**：客户端验证用户输入
4. **数据提交**：将更新提交到后端API
5. **状态管理**：管理编辑状态和加载状态

## 测试指南

### 前置条件
1. 确保后端API已正确实现并部署
2. 车辆管理图标（clgl.png）需要上传到CDN
3. 员工账号已绑定车辆

### 测试步骤
1. **访问功能**
   - 登录员工账号
   - 进入"我的"页面
   - 点击"车辆管理"图标

2. **查看车辆信息**
   - 验证车辆基本信息显示正确
   - 验证车辆详细信息显示正确

3. **编辑车辆信息**
   - 点击"编辑"按钮
   - 修改各个字段
   - 测试日期选择器功能
   - 点击"保存"提交更新

4. **数据验证测试**
   - 输入无效的里程数（负数、非数字）
   - 输入超长的外观描述（>1000字符）
   - 输入超长的物资清单（>2000字符）

5. **权限测试**
   - 使用未分配车辆的员工账号访问
   - 验证错误提示是否正确

### 预期结果
- 页面正常加载和显示
- 编辑功能正常工作
- 数据验证正确执行
- 权限控制有效
- 更新记录正确显示

## 注意事项

1. 确保后端API已正确实现并部署
2. 车辆管理图标（clgl.png）需要上传到CDN
3. 页面支持下拉刷新功能
4. 所有字段均为可选，不影响现有数据
5. 日期选择器使用微信小程序原生picker组件
