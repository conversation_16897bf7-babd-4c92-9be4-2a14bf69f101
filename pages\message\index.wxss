/* pages/message/index.wxss */
.containermessage{
  background-color: #f5f5f5;
  /* 为底部导航预留固定空间，避免页面加载时高度计算不准确 */
  padding-bottom: calc(100rpx + env(safe-area-inset-bottom));
  padding: 24rpx;
  box-sizing: border-box;
  min-height: 100vh;
}
.title-left{
  padding-left: 40rpx;
  font-size: 36rpx;
  font-weight: bold;
}
.contentmessage{
  box-shadow: 0px 0px 8px 0px rgba(0, 0, 0, 0.1);
  background-color: white;
  border-radius: 24rpx;
  /* 调整高度计算，减去导航栏和底部导航的高度 */
  height: calc(100vh - 60px - 100rpx - env(safe-area-inset-bottom) - 48rpx);
  overflow-y: auto;
}
.listwrapper{
  padding: 24rpx;
}
.flex3-clz {
  padding: 16rpx;
  border-bottom: 1px solid #dadada;
  transition: all 0.3s ease;
}

/* 未读消息样式 */
.message-unread {
  background-color: #f8f9ff;
  border-left: 4rpx solid #007aff;
}

/* 已读消息样式 */
.message-read {
  opacity: 0.7;
}

.message-icon-container {
  position: relative;
  flex-shrink: 0;
}

.image3-clz {
	flex-shrink: 0;
	border-radius: 100rpx;
	overflow: hidden;
	width: 100rpx !important;
}

.image3-size {
	height: 100rpx !important;
	width: 100rpx !important;
}

/* 未读指示器 */
.unread-indicator {
  position: absolute;
  top: -4rpx;
  right: -4rpx;
  width: 16rpx;
  height: 16rpx;
  background-color: #ff4757;
  border-radius: 50%;
  border: 2rpx solid white;
}
.flex5-clz {
	overflow: hidden;
	padding:0 0 0 10rpx;
}
.text8-clz {
	color: #5d5d5d;
	font-size: 28rpx !important;
}

/* 未读文本样式 */
.unread-text {
  font-weight: bold;
  color: #333 !important;
}

/* 已读文本样式 */
.read-text {
  font-weight: normal;
  color: #999 !important;
}
.text10-clz {
	color: #5d5d5d;
	flex: 1;
}
.text11-clz {
	background-color: #fa2e2e;
  border-radius: 30rpx;
  width: 48rpx;
  line-height: 48rpx;
  text-align: center;
	overflow: hidden;
	color: #ffffff;
	font-size: 20rpx;
}

/* 引入底部导航布局通用样式 */
@import "/common/tabbar-layout.wxss";