.additional-services {
  padding: 24rpx;
}

.service-item {
  border: 1rpx solid #e8e8e8;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 20rpx;
  background: #fff;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
  transition: all 0.2s ease;
}

.service-item:last-child {
  margin-bottom: 0;
}

.service-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.service-name {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
}

.service-status {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  color: #fff;
}

.service-status.pending {
  background: #ff9500;
}

.service-customer-info {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
  padding: 12rpx 16rpx;
  background: #f8f9fa;
  border-radius: 8rpx;
}

/* 服务项目列表样式 */
.service-items-list {
  margin-bottom: 16rpx;
  padding: 16rpx;
  background: #f8f9fa;
  border-radius: 8rpx;
}

.service-items-header {
  margin-bottom: 12rpx;
}

.items-title {
  font-size: 26rpx;
  font-weight: 600;
  color: #666;
}

.service-item-detail {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8rpx 0;
  border-bottom: 1rpx solid #e8e8e8;
}

.service-item-detail:last-child {
  border-bottom: none;
}

.item-info {
  display: flex;
  align-items: center;
  flex: 1;
}

.item-name {
  font-size: 28rpx;
  color: #333;
  margin-right: 8rpx;
}

.item-quantity {
  font-size: 24rpx;
  color: #666;
  background: #e8e8e8;
  padding: 2rpx 8rpx;
  border-radius: 4rpx;
}

.item-price {
  font-size: 28rpx;
  font-weight: 600;
  color: #007aff;
}

.customer-label {
  font-size: 26rpx;
  color: #666;
  flex-shrink: 0;
}

.customer-name {
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
  margin-right: 8rpx;
}

.customer-phone {
  font-size: 24rpx;
  color: #999;
}

.service-details {
  margin-bottom: 16rpx;
  padding: 12rpx 16rpx;
}

.detail-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8rpx;
}

.detail-label {
  font-size: 26rpx;
  color: #666;
  flex-shrink: 0;
}

.detail-value {
  font-size: 26rpx;
  color: #333;
}

.detail-time {
  color: #999;
  font-size: 24rpx;
}

.service-price-info {
  margin-bottom: 16rpx;
  padding: 12rpx 16rpx;
  background: #fff8f0;
  border-radius: 8rpx;
  border-left: 4rpx solid #ff9500;
}

.price-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 4rpx;
}

.price-row:last-child {
  margin-bottom: 0;
}

.price-label {
  font-size: 26rpx;
  color: #666;
}

.original-price {
  font-size: 24rpx;
  color: #999;
  text-decoration: line-through;
}

.current-price {
  font-size: 28rpx;
  color: #ff9500;
  font-weight: bold;
}

.service-actions {
  display: flex;
  gap: 16rpx;
  justify-content: flex-end;
  margin-top: 20rpx;
}

.service-action-btn {
  padding: 12rpx 32rpx;
  border-radius: 40rpx;
  font-size: 26rpx;
  text-align: center;
  min-width: 120rpx;
  transition: all 0.2s ease;
}

.confirm-btn {
  background: rgba(76, 175, 80, 1);
  color: white;
}

.confirm-btn:active {
  background: rgba(76, 175, 80, 0.8);
  transform: scale(0.95);
}

.reject-btn {
  background: rgba(244, 67, 54, 1);
  color: white;
}

.reject-btn:active {
  background: rgba(244, 67, 54, 0.8);
  transform: scale(0.95);
}
