.service-duration-section {
  background-color: #fff;
  padding: 20rpx;
  border-radius: 10rpx;
  margin-bottom: 20rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  padding-bottom: 10rpx;
  border-bottom: 1rpx solid #eee;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.toggle-icon {
  font-size: 24rpx;
  color: #666;
  transition: transform 0.3s ease;
}

.service-section-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin: 20rpx 0 15rpx 0;
  padding-bottom: 8rpx;
  position: relative;
  border-bottom: 2rpx solid #f0f0f0;
}

.service-section-title::before {
  content: '';
  position: absolute;
  left: 0;
  bottom: -2rpx;
  width: 60rpx;
  height: 4rpx;
  background: linear-gradient(135deg, #3083FF 0%, #764ba2 100%);
  border-radius: 2rpx;
}

.service-duration-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  margin-bottom: 15rpx;
  border-radius: 10rpx;
  border: 2rpx solid #f0f0f0;
  transition: all 0.3s ease;
}

.service-duration-item.main-service {
  background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
  border-color: #1890ff;
}

.service-duration-item.additional-service {
  background: linear-gradient(135deg, #fff8e1 0%, #ffecb3 100%);
  border-color: #ff9800;
  border-width: 2rpx;
}

.duration-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.service-name-row {
  display: flex;
  align-items: center;
  gap: 12rpx;
  flex-wrap: wrap;
}

.service-name {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
}

.service-status {
  font-size: 22rpx;
  padding: 6rpx 16rpx;
  border-radius: 16rpx;
  align-self: flex-start;
  font-weight: 500;
}

.service-status.not_started {
  background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%);
  color: #fff;
}

.service-status.running {
  background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
  color: #fff;
  animation: pulse 2s infinite;
}

.service-status.completed {
  background: linear-gradient(135deg, #00b894 0%, #00a085 100%);
  color: #fff;
}

@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.7; }
  100% { opacity: 1; }
}

.time-info {
  display: flex;
  flex-direction: column;
  gap: 4rpx;
}

.start-time,
.current-duration,
.duration {
  font-size: 24rpx;
  color: #666;
}

.current-duration {
  color: #1890ff;
  font-weight: bold;
}

.duration {
  color: #52c41a;
  font-weight: bold;
}

.duration-actions {
  display: flex;
  flex-direction: column;
  gap: 10rpx;
}

.duration-actions .action-btn {
  padding: 12rpx 24rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 80rpx;
}

.end-btn {
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
  color: #fff;
  box-shadow: 0 4rpx 12rpx rgba(255, 107, 107, 0.3);
}

.end-btn:active {
  transform: scale(0.95);
  box-shadow: 0 2rpx 6rpx rgba(255, 107, 107, 0.3);
}

.start-btn {
  background: linear-gradient(135deg, #00b894 0%, #00a085 100%);
  color: #fff;
  box-shadow: 0 4rpx 16rpx rgba(0, 184, 148, 0.3);
}

.start-btn:active {
  transform: scale(0.95);
  box-shadow: 0 2rpx 8rpx rgba(0, 184, 148, 0.3);
}

.completed-services-summary {
  /* 暂时隐藏 */
  display: none;
  margin-top: 20rpx;
}

.summary-info {
  background: #f8f9fa;
  border-radius: 12rpx;
  padding: 20rpx;
  margin-top: 10rpx;
}

.summary-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
}

.summary-item:last-child {
  margin-bottom: 0;
}

.summary-label {
  font-size: 26rpx;
  color: #666;
}

.summary-value {
  font-size: 26rpx;
  color: #333;
  font-weight: bold;
}

/* 付款状态样式 */
.payment-status {
  font-size: 20rpx;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  font-weight: 500;
}

.payment-status.pending_payment {
  background: #ffebee;
  color: #d32f2f;
  border: 1rpx solid #ffcdd2;
}

.payment-status.confirmed {
  background: #fff3e0;
  color: #f57c00;
  border: 1rpx solid #ffcc02;
}

.payment-status.paid {
  background: #e8f5e8;
  color: #2e7d32;
  border: 1rpx solid #c8e6c9;
}

/* 未付款提示样式 */
.unpaid-notice {
  margin-top: 8rpx;
  padding: 8rpx 12rpx;
  background: #fff3cd;
  border: 1rpx solid #ffeaa7;
  border-radius: 6rpx;
  border-left: 4rpx solid #fdcb6e;
}

.notice-text {
  font-size: 22rpx;
  color: #856404;
  line-height: 1.3;
}
