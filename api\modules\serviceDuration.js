import request, { analysisRes } from "../request";
import config from "../config";

const { serviceDuration } = config.apiUrls;

export default {
  /**
   * 开始整体订单服务
   * @param {Object} params 开始整体订单服务参数
   * @param {number} params.orderId 订单ID
   * @param {Array} [params.beforePhotos] 服务前照片数组
   */
  async startOrderService(params) {
    const res = await request.post(serviceDuration.startOrderService, params);
    return analysisRes(res);
  },

  /**
   * 开始单个服务
   * @param {Object} params 开始服务参数
   * @param {number} params.orderId 订单ID
   * @param {number} [params.orderDetailId] 订单详情ID（主服务时必填）
   * @param {number} [params.additionalServiceOrderId] 追加服务订单ID（增项服务时必填）
   * @param {string} params.recordType 记录类型：main_service/additional_service
   * @param {number} [params.serviceId] 服务ID（主服务时必填）
   * @param {number} [params.additionalServiceId] 增项服务ID（增项服务时必填）
   * @param {string} [params.remark] 备注
   */
  async start(params) {
    const res = await request.post(serviceDuration.start, params);
    return analysisRes(res);
  },

  /**
   * 结束服务
   * @param {Object} params 结束服务参数
   * @param {number} params.recordId 记录ID
   * @param {string} [params.remark] 备注
   */
  async end(params) {
    const res = await request.post(serviceDuration.end, params);
    return analysisRes(res);
  },

  /**
   * 查询订单服务状态
   * @param {number} orderId 订单ID
   * @param {boolean} forceRefresh 是否强制刷新
   */
  async getOrderServiceStatus(orderId, forceRefresh = false) {
    const params = {
      hideLoading: true,
    };

    // 如果需要强制刷新，添加时间戳参数
    if (forceRefresh) {
      params._t = Date.now();
    }

    const res = await request.get(serviceDuration.orderServiceStatus.replace('{orderId}', orderId), params);
    return analysisRes(res);
  },

  /**
   * 查询订单服务时长记录
   * @param {number} orderId 订单ID
   */
  async getRecords(orderId) {
    const res = await request.get(serviceDuration.records.replace('{orderId}', orderId), {
      hideLoading: true,
    });
    const result = analysisRes(res);
    // 新接口返回格式：{ orderId, records, statistics }
    return result;
  },

  /**
   * 获取当前进行中的服务
   */
  async getCurrent() {
    const res = await request.get(serviceDuration.current, {
      hideLoading: true,
    });
    return analysisRes(res);
  },

  /**
   * 查询我的服务时长记录
   * @param {Object} params 查询参数
   * @param {number} [params.orderId] 订单ID（可选）
   */
  async getMyRecords(params = {}) {
    const res = await request.get(serviceDuration.myRecords, {
      hideLoading: true,
      ...params,
    });
    return analysisRes(res);
  },
};
