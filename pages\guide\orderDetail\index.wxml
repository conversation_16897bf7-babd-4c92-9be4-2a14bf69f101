<!--pages/guide/orderDetail/index.wxml-->
<view class="demo-detail-page" wx:if="{{orderInfo}}">
  <!-- 演示提示 -->
  <view class="demo-banner">
    <text class="demo-icon">👀</text>
    <text class="demo-text">登录后可使用完整功能</text>
  </view>

  <!-- 订单基本信息 -->
  <view class="order-basic-info">
    <view class="order-header">
      <text class="order-id">{{orderInfo.orderId}}</text>
      <view class="order-status">
        <text class="status-text">待接单</text>
      </view>
    </view>
    <text class="order-time">下单时间：{{orderInfo.orderTime}}</text>
  </view>

  <!-- 服务信息 -->
  <view class="service-info-card">
    <view class="card-title">
      <text class="title-icon">🛁</text>
      <text class="title-text">服务信息</text>
    </view>
    <view class="service-content">
      <view class="service-item">
        <text class="service-label">服务类型：</text>
        <text class="service-value">{{orderInfo.serviceName[0]}}</text>
      </view>
      <view class="service-item" wx:if="{{orderInfo.extraServive.length > 0}}">
        <text class="service-label">增项服务：</text>
        <text class="service-value">{{orderInfo.extraServive.join('、')}}</text>
      </view>
      <view class="service-item">
        <text class="service-label">期望时间：</text>
        <text class="service-value">{{orderInfo.expectTime}}</text>
      </view>
      <view class="service-item">
        <text class="service-label">服务备注：</text>
        <text class="service-value">{{orderInfo.remark}}</text>
      </view>
    </view>
  </view>

  <!-- 客户信息 -->
  <view class="customer-info-card">
    <view class="card-title">
      <text class="title-icon">👤</text>
      <text class="title-text">客户信息</text>
    </view>
    <view class="customer-content">
      <view class="customer-item">
        <text class="customer-label">客户姓名：</text>
        <text class="customer-value">{{orderInfo.customerName}}</text>
      </view>
      <view class="customer-item">
        <text class="customer-label">联系电话：</text>
        <text class="customer-value">{{orderInfo.phone}}</text>
        <view class="phone-btn" bindtap="makePhoneCall">
          <text class="phone-icon">📞</text>
        </view>
      </view>
      <view class="customer-item">
        <text class="customer-label">服务地址：</text>
        <text class="customer-value">{{orderInfo.address}}</text>
        <view class="nav-btn" bindtap="openNavigation">
          <text class="nav-icon">🧭</text>
        </view>
      </view>
      <view class="customer-item">
        <text class="customer-label">宠物信息：</text>
        <text class="customer-value">{{orderInfo.petInfo}}</text>
      </view>
    </view>
  </view>

  <!-- 价格信息 -->
  <view class="price-info-card">
    <view class="card-title">
      <text class="title-icon">💰</text>
      <text class="title-text">价格信息</text>
    </view>
    <view class="price-content">
      <view class="price-item" wx:if="{{orderInfo.originalPrice !== orderInfo.totalFee}}">
        <text class="price-label">原价：</text>
        <text class="original-price">¥{{orderInfo.originalPrice}}</text>
      </view>
      <view class="price-item">
        <text class="price-label">实付金额：</text>
        <text class="total-price">¥{{orderInfo.totalFee}}</text>
      </view>
    </view>
  </view>

  <!-- 底部操作按钮 -->
  <view class="bottom-actions">
    <button class="accept-btn" bindtap="acceptOrder">
      <text class="btn-icon">✋</text>
      <text class="btn-text">接单</text>
    </button>
    <view class="demo-tip">
      <text>登录后可进行接单操作</text>
    </view>
  </view>
</view>

<!-- 加载状态 -->
<view class="loading-state" wx:else>
  <text>加载中...</text>
</view>
