/* pages/mine/profile/index.wxss */

/* 遮罩层 */
.modal-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
  animation: fadeIn 0.3s ease-out;
}

/* 弹窗内容 */
.modal-content {
  background-color: #fff;
  border-radius: 24rpx;
  width: 100%;
  max-width: 640rpx;
  max-height: 80vh;
  overflow: hidden;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.15);
  animation: slideUp 0.3s ease-out;
}

/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(100rpx);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* 标题栏 */
.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 40rpx 40rpx 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.modal-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}

.close-btn {
  font-size: 40rpx;
  color: #999;
  padding: 10rpx;
  line-height: 1;
}

.close-btn:active {
  color: #666;
}

/* 表单内容 */
.modal-body {
  padding: 40rpx;
  max-height: 60vh;
  overflow-y: auto;
}

.form-item {
  margin-bottom: 40rpx;
}

.form-item:last-child {
  margin-bottom: 0;
}

.form-label {
  display: block;
  font-size: 30rpx;
  color: #333;
  margin-bottom: 20rpx;
  font-weight: 500;
}

/* 头像编辑样式 */
.avatar-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20rpx;
}

.avatar-container {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  overflow: hidden;
  border: 4rpx solid #f0f0f0;
}

.avatar-image {
  width: 100%;
  height: 100%;
}

.avatar-btn {
  background-color: #f8f8f8;
  color: #666;
  border: none;
  border-radius: 40rpx;
  padding: 16rpx 32rpx;
  font-size: 28rpx;
  line-height: 1;
}

.avatar-btn:active {
  background-color: #e8e8e8;
}

.avatar-btn.loading {
  background-color: #f0f0f0;
  color: #999;
}

/* 输入框样式 */
.input-wrapper {
  position: relative;
}

.form-input {
  width: 100%;
  height: 80rpx;
  padding: 0 20rpx;
  padding-right: 80rpx;
  border: 2rpx solid #e5e5e5;
  border-radius: 12rpx;
  font-size: 30rpx;
  color: #333;
  background-color: #fff;
  box-sizing: border-box;
}

.form-input:focus {
  border-color: #007AFF;
  outline: none;
}

.form-input::placeholder {
  color: #999;
}

.input-counter {
  position: absolute;
  right: 20rpx;
  top: 50%;
  transform: translateY(-50%);
  font-size: 24rpx;
  color: #999;
}

/* 只读输入框 */
.readonly-input {
  height: 80rpx;
  padding: 0 20rpx;
  background-color: #f8f8f8;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
}

.readonly-text {
  font-size: 30rpx;
  color: #999;
}

/* 底部按钮 */
.modal-footer {
  display: flex;
  padding: 30rpx 40rpx 40rpx;
  gap: 20rpx;
  border-top: 1rpx solid #f0f0f0;
}

.btn {
  flex: 1;
  height: 88rpx;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: 500;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-cancel {
  background-color: #f8f8f8;
  color: #666;
}

.btn-cancel:active {
  background-color: #e8e8e8;
}

.btn-save {
  background-color: #007AFF;
  color: #fff;
}

.btn-save:active {
  background-color: #0056CC;
}

.btn-save.loading {
  background-color: #ccc;
  color: #999;
}

.btn:disabled {
  background-color: #f0f0f0;
  color: #ccc;
}
