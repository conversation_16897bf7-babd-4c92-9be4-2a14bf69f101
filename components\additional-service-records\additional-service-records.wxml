<!-- 追加服务记录（非待确认状态） -->
<view class="all-additional-services" wx:if="{{confirmedAdditionalServiceOrders.length > 0}}">
  <view class="service-item" wx:for="{{confirmedAdditionalServiceOrders}}" wx:key="id">
    <view class="service-info">
      <view class="service-header">
        <text class="service-name">追加服务</text>
        <text class="service-status {{item.status}}">{{item.statusText}}</text>
      </view>

      <!-- 客户信息 -->
      <view class="service-customer-info">
        <text class="customer-label">客户：</text>
        <text class="customer-name">{{item.customer.name || item.customer.nickname || '未知客户'}}</text>
        <text class="customer-phone" wx:if="{{item.customer.phone}}">({{item.customer.phone}})</text>
      </view>

      <!-- 订单信息 -->
      <view class="service-details">
        <view class="detail-row">
            <text class="detail-label">订单号：</text>
            <text class="detail-value">{{item.sn}}</text>
          </view>
          <view class="detail-row" wx:if="{{item.createdAt}}">
            <text class="detail-label">申请时间：</text>
            <text class="detail-value detail-time">{{item.createdAt}}</text>
          </view>
          <view class="detail-row" wx:if="{{item.confirmTime}}">
            <text class="detail-label">处理时间：</text>
            <text class="detail-value detail-time">{{item.confirmTime}}</text>
          </view>
      </view>

      <!-- 服务项目列表 -->
      <view class="service-items-list">
        <view class="service-items-header">
          <text class="items-title">服务项目：</text>
        </view>
        <view class="service-item-detail" wx:for="{{item.details}}" wx:for-item="detail" wx:key="id">
          <view class="item-info">
            <text class="item-name">{{detail.additionalServiceName || detail.serviceName}}</text>
            <text class="item-quantity" wx:if="{{detail.quantity > 1}}">×{{detail.quantity}}</text>
          </view>
          <text class="item-price">¥{{detail.price || detail.servicePrice}}</text>
        </view>
      </view>

      <!-- 价格信息 -->
      <view class="service-price-info">
        <view class="price-row" wx:if="{{item.originalPrice && item.originalPrice > 0}}">
          <text class="price-label">原价：</text>
          <text class="original-price">¥{{item.originalPrice}}</text>
        </view>
        <view class="price-row">
          <text class="price-label">实付：</text>
          <text class="current-price">¥{{item.totalFee || item.totalServicePrice}}</text>
        </view>
      </view>

      <!-- 拒绝原因（仅拒绝状态显示） -->
      <view class="reject-reason" wx:if="{{item.status === 'rejected' && item.rejectReason}}">
        <text class="reason-label">拒绝原因：</text>
        <text class="reason-text">{{item.rejectReason}}</text>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view class="service-actions" wx:if="{{item.status === 'pending_confirm' || (item.status === 'pending_payment' || item.status === 'confirmed')}}">
      <!-- 待确认状态的操作按钮 -->
      <block wx:if="{{item.status === 'pending_confirm'}}">
        <view class="service-action-btn confirm-btn" bindtap="onConfirmService" data-service="{{item}}">
          确认
        </view>
        <view class="service-action-btn reject-btn" bindtap="onRejectService" data-service="{{item}}">
          拒绝
        </view>
      </block>

      <!-- 未付款状态的删除按钮 -->
      <block wx:if="{{item.status !== 'pending_confirm' && item.status !== 'paid' && item.status !== 'refunding' && item.status !== 'completed'}}">
        <view class="service-action-btn delete-btn" bindtap="onDeleteService" data-service="{{item}}">
          删除
        </view>
      </block>
    </view>
  </view>
</view>
