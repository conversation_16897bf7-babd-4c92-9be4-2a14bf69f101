/* 
 * 底部导航布局通用样式
 * 解决页面首次加载时图片拉伸问题
 */

/* 通用容器样式 - 为底部导航预留空间 */
.tabbar-container {
  min-height: 100vh;
  /* 为底部导航预留固定空间，避免页面加载时高度计算不准确 */
  padding-bottom: calc(100rpx + env(safe-area-inset-bottom));
  box-sizing: border-box;
}

/* 滚动区域样式 - 确保内容不被底部导航遮挡 */
.tabbar-scroll-view {
  /* 减去导航栏(60px)、标签栏(120rpx)、底部导航(100rpx)和安全区域的高度 */
  height: calc(100vh - 60px - 120rpx - 100rpx - env(safe-area-inset-bottom));
  padding-bottom: 20rpx;
}

/* 消息页面特殊样式 */
.tabbar-message-content {
  /* 减去导航栏和底部导航的高度，留出适当间距 */
  height: calc(100vh - 60px - 100rpx - env(safe-area-inset-bottom) - 48rpx);
}

/* 底部导航图片固定尺寸，避免拉伸 */
.tabbar-image {
  width: 56rpx !important;
  height: 56rpx !important;
  flex-shrink: 0;
}
