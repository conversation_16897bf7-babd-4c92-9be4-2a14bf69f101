<view class="custom-bottom-bar">
  <view class="flex diygw-col-24 diygw-bottom">
    <view class="diygw-grid diygw-actions">
      <button class="diygw-action" data-type="index" bind:tap="redirect">
        <view class="diygw-grid-inner">
          <image src="//xian7.zos.ctyun.cn/pet/static/{{currentActive === 'home' ? 'jddt-b':'jddt'}}.png" class="image7-size diygw-image image-sm tabbar-image" mode="aspectFit"></image>
          <view class="diygw-grid-title"> 接单大厅 </view>
        </view>
      </button>
      <button class="diygw-action" data-type="orders" bind:tap="redirect">
        <view class="diygw-grid-inner">
          <image src="//xian7.zos.ctyun.cn/pet/static/{{currentActive === 'service' ? 'orders-b':'orders'}}.png" class="image7-size diygw-image image-sm tabbar-image" mode="aspectFit"></image>
          <view class="diygw-grid-title"> 我的订单 </view>
        </view>
      </button>
      <button class="diygw-action" data-type="message" bind:tap="redirect">
        <view class="diygw-grid-inner">
          <!-- <view class="diygw-tag badge bar-badge-1 red"> 1 </view> -->
          <!-- <view class="diygw-tag badge empty bar-badge-2 red"></view> -->
          <image src="//xian7.zos.ctyun.cn/pet/static/{{currentActive === 'message' ? 'message-b':'message'}}.png" class="image7-size diygw-image image-sm tabbar-image" mode="aspectFit"></image>
          <view class="diygw-grid-title"> 消息 </view>
        </view>
      </button>
      <button class="diygw-action" data-type="mine" bind:tap="redirect">
        <view class="diygw-grid-inner">
          <image src="//xian7.zos.ctyun.cn/pet/static/{{currentActive === 'mine' ? 'mine-b':'mine'}}.png" class="image7-size diygw-image image-sm tabbar-image" mode="aspectFit"></image>
          <view class="diygw-grid-title"> 我的 </view>
        </view>
      </button>
    </view>
  </view>
  <view class="clearfix"></view>
</view>